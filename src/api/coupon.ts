import { http } from "@/utils/http/request";

enum Api {
  CouponCategoryList = "/rest/operation/coupon/categoryList",
  CouponPage = "/rest/operation/coupon/pageInfo",
  CouponStorePage = "/rest/operation/coupon/storePage/{couponId}",
  CouponStatistics = "/rest/operation/coupon/statistics",
  CouponUserWaitList ='/rest/operation/coupon/userWaitList'
}

export function apiCouponCategoryList(params?: any) {
  return http({
    url: Api.CouponCategoryList,
    params,
    method: "GET",
  });
}

export function apiCouponPage(params?: any) {
  return http({
    url: Api.CouponPage,
    params,
    method: "GET",
  });
}

export function apiCouponStorePageList({ id, ...params }: any) {
  return http({
    url: Api.CouponStorePage.replace("{couponId}", id),
    method: "GET",
    params,
    isLoading: false,
  });
}

export function apiCouponStatistics() {
  return http({
    url: Api.CouponStatistics,
    method: "GET",
  });
}


// 获取带领取的消费券
export function apiCouponUserWaitList(params?: any) {
  return http({
    url: Api.CouponUserWaitList,
    method: "GET",
  });
}