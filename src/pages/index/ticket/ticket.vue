<script lang="ts" setup>
	import TicketCard from './ticket-card.vue'
	import { apiCouponCategoryList, apiCouponPage, apiCouponUserWaitList } from '@/api/coupon'
	import { apiMemberReceive } from '@/api/member'
	import { useScrollView } from '@/hook/useScrollView'
	import { map } from 'lodash-es'
	import { useMessage } from 'wot-design-uni'

	const props = defineProps<{
		cacheTicketCategoryId?: string | null
		cacheUseState?: number | null
	}>()

	const emits = defineEmits(['update:cacheTicketCategoryId'])

	const message = useMessage()
	// "AWAIT",
	const useStateMap = ['AWAIT', 'INIT', 'OVERDUE']

	const category = ref(props.cacheTicketCategoryId)

	const useState = ref(props.cacheUseState || 0)

	const useStateType = computed(() => {
		if (unref(useState) >= 0) {
			return useStateMap[unref(useState)]
		}
		return useStateMap[0]
	})

	// 计算属性查询参数
	const params = computed(() => {
		return {
			categoryId: unref(category),
			useState: unref(useStateType),
		}
	})

	const {
		loading,
		refresherStatus,
		onPullDownRefresh,
		onReachBottom,
		list,
		clearList,
		isEmpty,
		reload,
	} = useScrollView<any>({
		api: (params) => {
			if (params?.useState === 'AWAIT') {
				return apiCouponUserWaitList()
			}
			return apiCouponPage(params)
		},
		params,
	})
	const categoryList = ref([
		{
			label: '全部',
			value: null,
		},
	])

	const methods = {
		// 获取分类
		async getCategory() {
			categoryList.value = map((await apiCouponCategoryList()) || [], (item) => {
				return {
					label: item.name,
					value: item.id,
				}
			})
		},
		// 回调
		onGetCoupon(v: any) {
			message
				.confirm({
					title: '提示',
					msg: '确定领取该优惠券吗？',
				})
				.then(() => {
					apiMemberReceive(v)
						.then((res) => {
							console.log(res)
						})
						.finally(() => {
							reload()
						})
				})
				.catch((e) => {
					console.log('用户点击取消')
				})
		},
	}
	methods.getCategory()

	onMounted(() => {
		watch(params, (v) => {
			clearList()
			reload()
		})
		if (props.cacheTicketCategoryId) {
			emits('update:cacheTicketCategoryId', null)
		}
	})
</script>

<template>
	<view class="home-ticket">
		<wd-tabs v-model="useState" autoLineWidth>
			<wd-tab title="待领取"> </wd-tab>
			<wd-tab title="待使用"> </wd-tab>
			<wd-tab title="已过期"> </wd-tab>
		</wd-tabs>
		<zk-category
			v-if="useStateType !== 'AWAIT'"
			v-model:value="category"
			:options="categoryList"
		></zk-category>
		<scroll-view
			:scroll-y="true"
			class="common-scroll"
			:refresher-enabled="true"
			@refresherrefresh="onPullDownRefresh"
			:refresher-triggered="refresherStatus"
			@scrolltolower="onReachBottom"
		>
			<!-- 吸顶按钮 -->
			<view class="sticky-button-container">
				<wd-button type="success" class="sticky-button">基础用法</wd-button>
			</view>

			<view class="common-list" v-if="useStateType !== 'AWAIT'">
				<TicketCard
					:useState="useState"
					v-for="item in list"
					:item="item"
					:key="item.id"
					@onGetCoupon="methods.onGetCoupon(item)"
				/>
			</view>

			<view v-for="i in 100">{{ i }}</view>

			<view class="common-empty" v-if="isEmpty && useStateType !== 'AWAIT'">
				<wd-status-tip image="content" tip="暂无内容" />
			</view>
			<zk-loading :loading="loading"></zk-loading>
		</scroll-view>
	</view>
</template>

<style scoped lang="scss">
	.sticky-button-container {
		position: sticky;
		top: 0;
		background: #fff;
		padding: 24rpx;
		border-bottom: 1rpx solid #f0f0f0;
	}

	.home-ticket {
		display: flex;
		flex-direction: column;
		box-sizing: border-box;
		height: 100%;
		position: relative;

		&-list {
			display: flex;
			flex-direction: column;
			gap: 24rpx;
			padding: 24rpx;
			box-sizing: border-box;
		}
		&-empty {
			position: fixed;
			top: 50%;
			left: 50%;
			transform: translate(-50%, -50%);
			margin-top: -50rpx;
		}
	}
</style>

<style lang="scss">
	.nut-tab-pane {
		display: none;
	}
</style>
