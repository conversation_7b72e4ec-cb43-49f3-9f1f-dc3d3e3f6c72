<script setup lang="ts">
	import { goToPage } from '@/router/topage'
	import { get } from 'lodash-es'
	interface Props {
		item: any
		useState: number
	}
	const props = defineProps<Props>()
	const emit = defineEmits(['onGetCoupon'])
	// 时间范围展示
	const showTime = computed(() => {
		return `${get(props.item, 'operationCoupon.beginDate')?.replaceAll('-', '.')}-${get(props.item, 'operationCoupon.endDate')?.replaceAll('-', '.')}`
	})

	const methods = {
		// 去使用
		goToPage() {
			uni.navigateTo({
				url: '/pages/ticket/ticket',
				success: ({ eventChannel }) => {
					console.log('跳转成功')
					eventChannel.emit('couponInfo', toRaw(props.item))
				},
			})
		},
		getCoupon() {
			emit('onGetCoupon')
		},
	}
</script>

<template>
	<view class="home-ticket-card">
		<view class="home-ticket-card-left">
			<view class="home-ticket-card-left-price">
				<view class="symbol">¥</view>
				<view>{{ get(item, 'operationCoupon.price') }}</view>
			</view>
			<view class="home-ticket-card-left-tips">
				满{{ get(item, 'operationCoupon.threshold') }}可用
			</view>
		</view>
		<view class="home-ticket-card-info">
			<view class="name"> {{ get(item, 'operationCoupon.title') }} </view>
			<view class="time">
				{{ showTime }}
			</view>
		</view>
		<view class="home-ticket-card-action">
			<nut-button
				v-if="useState === 0"
				size="small"
				type="danger"
				@click="methods.getCoupon()"
			>
				领取
			</nut-button>
			<nut-button
				v-else-if="useState === 1"
				size="small"
				type="danger"
				plain
				@click="methods.goToPage()"
			>
				待使用
			</nut-button>
			<image
				class="home-ticket-card-icon-overdue"
				v-else-if="useState === 2"
				src="https://tzcjlw.oss-cn-shanghai.aliyuncs.com/mini-program/expire.png"
			></image>
		</view>
	</view>
</template>

<style scoped lang="scss">
	.home-ticket-card {
		height: 158rpx;
		border-radius: 16rpx;
		background-color: #fff;
		display: flex;
		justify-content: space-between;
		overflow: hidden;
		&-icon {
			&-overdue {
				width: 100rpx;
				height: 100rpx;
			}
		}
		&-left {
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: center;
			gap: 8rpx;
			width: 0;
			flex-basis: 205rpx;
			background: #ffebeb;
			&-price {
				display: flex;
				gap: 4rpx;
				color: #f05338;
				font-size: 56rpx;
				font-weight: 600;
				line-height: 56rpx;
				align-items: flex-end;
				margin-top: 10rpx;
				& .symbol {
					font-size: 20rpx;
					line-height: 20rpx;
					padding-bottom: 10rpx;
				}
			}
			&-tips {
				color: #f05338;
				text-align: center;
				font-size: 24rpx;
			}
		}
		&-info {
			display: flex;
			flex-direction: column;
			justify-content: center;
			flex-grow: 1;
			width: 0;
			flex-shrink: 0;
			padding-left: 37rpx;
			gap: 8rpx;

			& .name {
				color: #333;
				font-size: 32rpx;
				font-weight: 500;
				overflow: hidden; /* 隐藏超出的内容 */
				white-space: nowrap; /* 禁止换行 */
				text-overflow: ellipsis; /* 溢出时显示省略号 */
			}
			& .time {
				color: #999;
				font-size: 24rpx;
				font-weight: 400;
			}
		}
		&-action {
			display: flex;
			justify-content: center;
			align-items: center;
			padding-right: 24rpx;
		}
	}
</style>
