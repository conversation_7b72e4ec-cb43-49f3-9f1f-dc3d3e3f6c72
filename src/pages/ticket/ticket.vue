<template>
	<ly-info title="消费券" title-position="left">
		<view class="common-com common-pd container">
			<view class="container-box">
				<view class="container-center">
					<zk-text color="#1677FF" size="50" :text="`满${threshold}减${price}元`">
					</zk-text>
				</view>
				<view class="container-center container-des">
					<zk-text color="#999" size="26" :text="`有效期:${renderTime}`"> </zk-text>
				</view>
				<view class="container-center container-tips">
					<zk-text color="#333" size="30"> 请提醒商家 </zk-text>
					<zk-text color="#FB6917" size="30"> 扫码核销 </zk-text>
					<zk-text color="#333" size="30"> 使用 </zk-text>
				</view>
				<view v-if="qrcode" class="container-center container-qr">
					<uqrcode canvas-id="test" size="320" sizeUnit="rpx" :value="qrcode"></uqrcode>
				</view>
				<view class="container-center container-code">
					<zk-text color="#333" size="30" :text="`券码：${id}`"> </zk-text>
					<zk-text color="#1677FF" size="30" @click="methods.clipboard"> 复制 </zk-text>
				</view>
				<view class="container-center container-button">
					<nut-button type="primary" block @click="methods.goTicketStore()">
						查看可使用的门店
					</nut-button>
				</view>
			</view>
			<view class="container-footer">
				<zk-card title="使用须知">
					<zk-text> 为了保障您的权益，消费前请不要将二维码提供给商户 </zk-text>
					<template #extra>
						<zk-text
							color="#999"
							size="26"
							@click="
								goToPage('/pages/ticket-tips/ticket-tips', {
									renderTime,
								})
							"
						>
							查看更多 >
						</zk-text>
					</template>
				</zk-card>
			</view>
		</view>
	</ly-info>
</template>

<script setup lang="ts">
	import uqrcode from '@/components/uqrcode/uqrcode.vue'
	import { goToPage, goTicketStore } from '@/router/topage'
	import { get, pick } from 'lodash-es'
	import querystring from 'query-string'
	import { usePathData } from '@/hook/usePathData'

	// 优惠券数据
	const id = ref('')
	const beginDate = ref('')
	const endDate = ref('')
	const price = ref(0)
	const threshold = ref(0)
	const qrcode = ref('')

	const { eventData: pathData } = usePathData('couponInfo', ({ eventData: res }) => {
		qrcode.value = querystring.stringify({
			id: get(res, 'id'),
			...pick(get(res, 'operationCoupon', {}), ['endDate', 'beginDate', 'price']),
			time: Date.now(),
		})
		price.value = get(res, 'operationCoupon.price', 0)
		threshold.value = get(res, 'operationCoupon.threshold', 0)
		id.value = get(res, 'id', '')
		beginDate.value = get(res, 'operationCoupon.beginDate', '')
		endDate.value = get(res, 'operationCoupon.endDate', '')
	})

	// 有效期渲染
	const renderTime = computed(() => {
		return `${unref(beginDate).replaceAll('-', '.') + '-' + unref(endDate).replaceAll('-', '.')}`
	})

	const methods = {
		// 跳转到支持的商家列表
		goTicketStore() {
			goTicketStore({
				id: get(unref(pathData), 'operationCoupon.id'),
			})
		},
		clipboard() {
			uni.setClipboardData({
				data: unref(id),
				showToast: true,
			})
		},
	}
</script>

<style scoped lang="scss">
	.container {
		// 样式区域
		display: flex;
		flex-direction: column;

		&-footer {
			margin-top: 24rpx;
		}

		&-button {
			margin: 60rpx 40rpx 0 40rpx;
		}

		&-code {
			margin-top: 24rpx;
			display: flex;
			gap: 10rpx;
		}

		&-tips {
			margin-left: 40rpx;
			margin-right: 40rpx;
			border-radius: 10rpx;
			height: 80rpx;
			text-align: center;
			line-height: 80rpx;
			background: #fff2ed;
			margin-top: 52rpx;
		}
		&-qr {
			margin-top: 32rpx;
			min-height: 320rpx;
		}

		&-des {
			margin-top: 12rpx;
		}

		&-center {
			display: flex;
			align-items: center;
			justify-content: center;
		}

		&-box {
			padding-top: 80rpx;
			margin-top: 60rpx;
			width: 100%;
			flex-shrink: 0;
			border-radius: 20rpx;
			background: #fff;
			display: flex;
			flex-direction: column;
			padding-bottom: 60rpx;
		}
	}
</style>
